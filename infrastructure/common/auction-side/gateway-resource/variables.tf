variable "project_name" {
  description = "Project name"
}

variable "environment" {
  description = "the environment name such as prod or stage"
}

variable "allow_origin" {
  description = "allow_origin"
}

variable "s3-bucket-arn" {
  description = "File upload bucket arn"
}

variable "s3-bucket-id" {
  description = "File upload bucket id"
}

variable "aws_api_gateway_rest_api_gateway_id" {
  description = "Aws api gateway rest api"
  default     = ""
}

variable "root_resource_id" {
  description = "Aws api gateway root resource id"
  default     = ""
}

variable "aws_api_gateway_rest_api_gateway_execution_arn" {
  description = "Aws api gateway execution arn"
  default     = ""
}

variable "prefix_function_name" {
  description = "prefix_function_name"
  default     = "auction"
}

variable "lambda_subnet_ids" {
  description = "lambda_subnet_ids"
  default     = []
}

variable "lambda_security_group_id" {
  description = "lambda_security_group_id"
  default     = ""
}

variable "lambda_global_environment_variables" {
  description = "lambda_global_environment_variables"
  default     = {}
}

variable "slack-notification-lambda-arn" {
  description = "slack-notification-lambda-arn"
}

variable "common_lambda_layer" {
  description = "common_lambda_layer"
}

variable "libreoffice_lib_layer" {
  description = "libreoffice_lib_layer"
}

variable "convert_xlsx_to_pdf_endpoint_lambda_arn" {
  description = "convert_xlsx_to_pdf_endpoint_lambda_arn"
}

variable "cognito_user_pool_arn" {
  description = "Cognito User Pool ARN"
}

variable "cognito_user_pool_id" {
  description = "Cognito User Pool ID"
}

variable "cognito_client_id" {
  description = "Cognito Client ID"
}
