VITE_API_ENDPOINT='https://d13mmha1gm19pv.cloudfront.net/api/'
VITE_CRYPTOJS_IV='IT2HUGkWgyHfeS0w'
VITE_CRYPTOJS_KEY='DtSW1F5VvcMZBlArtjbBwlW5szfWK2Pr'
VITE_DESIGN_JAVASCRIPT_PATH='/js/'
VITE_GA_ID=''
VITE_LOCALSTORAGE_LOGIN_INFO_LABEL='xxxxx'
VITE_NODE_ENV='dev'
VITE_TIMEOUT=30000
VITE_WEB_SOCKET_ENDPOINT='wss://xxxx.execute-api.ap-northeast-1.amazonaws.com/api?token='

# Auction-specific Cognito User Pool configuration
# These values will be populated by Terraform outputs after deployment
VITE_AUCTION_USER_POOL_ID=ap-northeast-1_XXXXXXXXX
VITE_AUCTION_CLIENT_ID=XXXXXXXXXXXXXXXXXXXXXXXXXX
