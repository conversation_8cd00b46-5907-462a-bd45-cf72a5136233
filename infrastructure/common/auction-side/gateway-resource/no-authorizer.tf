
module "login" {
  source = "./login"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "request-member" {
  source = "./request-member"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  authorization = "NONE"
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-member-regist-constants" {
  source = "./get-member-regist-constants"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "reissue-password" {
  source = "./reissue-password"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  # TODO
  authorization = "NONE"
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-guide-constants" {
  source = "./get-guide-constants"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  authorization = "NONE"
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "get-auction-common-constants" {
  source = "./get-auction-common-constants"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  # TODO
  authorization = "NONE"
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

# Cognito Authentication Endpoints
module "cognito-login" {
  source = "./cognito-login"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  authorization = "NONE"
  cognito_user_pool_id = var.cognito_user_pool_id
  cognito_client_id = var.cognito_client_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}

module "cognito-register-member" {
  source = "./cognito-register-member"
  project_name = var.project_name
  environment = var.environment
  allow_origin = var.allow_origin
  s3-bucket-arn = var.s3-bucket-arn
  s3-bucket-id = var.s3-bucket-id
  aws_api_gateway_rest_api_gateway_id = var.aws_api_gateway_rest_api_gateway_id
  parent_id = var.root_resource_id
  prefix_function_name = var.prefix_function_name
  aws_api_gateway_rest_api_gateway_execution_arn = var.aws_api_gateway_rest_api_gateway_execution_arn
  lambda_subnet_ids = var.lambda_subnet_ids
  lambda_security_group_id = var.lambda_security_group_id
  lambda_global_environment_variables = var.lambda_global_environment_variables
  slack-notification-lambda-arn = var.slack-notification-lambda-arn
  authorization = "NONE"
  cognito_user_pool_id = var.cognito_user_pool_id
  cognito_client_id = var.cognito_client_id
  lambda_layer = [
    aws_lambda_layer_version.node_common_layer.arn,
    aws_lambda_layer_version.node_module_layer.arn,
    var.common_lambda_layer
  ]
}
