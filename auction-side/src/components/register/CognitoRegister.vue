<template>
  <div class="min-vh-100 d-flex flex-row align-items-center bg-light">
    <div class="container">
      <div class="row justify-content-center">
        <div class="col-md-6">
          <!-- Success Message Component -->
          <div v-if="registrationSuccess" class="card mx-4">
            <div class="card-body p-4 text-center">
              <div class="mb-4">
                <i
                  class="fas fa-check-circle text-success"
                  style="font-size: 3rem"
                ></i>
                <h3 class="mt-3">会員登録が完了しました</h3>
                <p class="text-muted">以下の情報でアカウントが作成されました</p>
              </div>

              <div class="card mb-4">
                <div class="card-body text-start">
                  <div class="mb-3">
                    <strong>会員名:</strong> {{ createdUser.memberName }}
                  </div>
                  <div class="mb-3">
                    <strong>メールアドレス:</strong> {{ createdUser.email }}
                  </div>
                  <div class="mb-3">
                    <strong>言語設定:</strong>
                    {{ getLanguageName(createdUser.language) }}
                  </div>
                  <div>
                    <strong>テナントID:</strong> {{ createdUser.tenantId }}
                  </div>
                </div>
              </div>

              <div class="d-flex justify-content-between">
                <button class="btn btn-secondary" @click="backToRegister">
                  戻る
                </button>
                <button class="btn btn-primary" @click="navigateToLogin">
                  ログイン画面へ
                </button>
              </div>
            </div>
          </div>

          <!-- Registration Form -->
          <div v-else class="card mx-4">
            <div class="card-body p-4">
              <form @submit.prevent="onSubmit">
                <h1 class="text-center">会員登録</h1>
                <p class="text-muted text-center">
                  オークション会員アカウントを作成
                </p>

                <!-- 会員名 -->
                <div class="mb-3">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-user"></i>
                    </span>
                    <input
                      v-model="formData.memberName"
                      type="text"
                      class="form-control"
                      :class="{'is-invalid': errors.memberName}"
                      placeholder="会員名"
                      @blur="validateField('memberName')"
                    />
                  </div>
                  <div
                    v-if="errors.memberName"
                    class="invalid-feedback d-block"
                  >
                    {{ errors.memberName }}
                  </div>
                </div>

                <!-- メールアドレス -->
                <div class="mb-3">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-envelope"></i>
                    </span>
                    <input
                      v-model="formData.email"
                      type="email"
                      class="form-control"
                      :class="{'is-invalid': errors.email}"
                      placeholder="メールアドレス"
                      @blur="validateField('email')"
                    />
                  </div>
                  <div v-if="errors.email" class="invalid-feedback d-block">
                    {{ errors.email }}
                  </div>
                </div>

                <!-- パスワード -->
                <div class="mb-3">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-lock"></i>
                    </span>
                    <input
                      v-model="formData.password"
                      type="password"
                      class="form-control"
                      :class="{'is-invalid': errors.password}"
                      placeholder="パスワード"
                      @blur="validateField('password')"
                    />
                  </div>
                  <div v-if="errors.password" class="invalid-feedback d-block">
                    {{ errors.password }}
                  </div>
                </div>

                <!-- パスワード（確認用） -->
                <div class="mb-3">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-lock"></i>
                    </span>
                    <input
                      v-model="formData.passwordConfirm"
                      type="password"
                      class="form-control"
                      :class="{'is-invalid': errors.passwordConfirm}"
                      placeholder="パスワード（確認用）"
                      @blur="validateField('passwordConfirm')"
                    />
                  </div>
                  <div
                    v-if="errors.passwordConfirm"
                    class="invalid-feedback d-block"
                  >
                    {{ errors.passwordConfirm }}
                  </div>
                </div>

                <!-- 言語設定 -->
                <div class="mb-4">
                  <div class="input-group">
                    <span class="input-group-text">
                      <i class="fas fa-globe"></i>
                    </span>
                    <select
                      v-model="formData.language"
                      class="form-select"
                      :class="{'is-invalid': errors.language}"
                      @blur="validateField('language')"
                    >
                      <option value="">言語設定を選択</option>
                      <option
                        v-for="opt in languageOptions"
                        :key="opt.value"
                        :value="opt.value"
                      >
                        {{ opt.label }}
                      </option>
                    </select>
                  </div>
                  <div v-if="errors.language" class="invalid-feedback d-block">
                    {{ errors.language }}
                  </div>
                </div>

                <!-- エラーメッセージ -->
                <div v-if="registerMsg.length > 0" class="mb-3">
                  <div
                    v-for="(msg, index) in registerMsg"
                    :key="index"
                    class="text-danger"
                  >
                    {{ msg }}
                  </div>
                </div>

                <!-- 登録ボタン -->
                <div class="d-grid">
                  <button
                    id="registerBtn"
                    class="btn btn-success"
                    type="submit"
                    :disabled="loading || !isFormValid"
                  >
                    <span
                      v-if="loading"
                      class="spinner-border spinner-border-sm me-2"
                      role="status"
                      aria-hidden="true"
                    ></span>
                    <span v-else>アカウント作成</span>
                  </button>
                </div>

                <!-- ログインリンク -->
                <div class="text-center mt-3">
                  <a
                    @click="navigateToLogin"
                    href="javascript:void(0)"
                    class="text-primary"
                  >
                    既存アカウントでログイン
                  </a>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  defineOptions({name: 'CognitoRegisterPage'})
  import {computed, reactive, ref} from 'vue'
  import {useRouter} from 'vue-router'
  import useApi from '../../composables/useApi'
  import {useCognitoAuthStore} from '../../stores/cognitoAuth'

  const router = useRouter()
  const cognitoAuth = useCognitoAuthStore()
  const {apiExecute} = useApi()

  const registerMsg = ref([])
  const loading = ref(false)
  const registrationSuccess = ref(false)
  const createdUser = ref({
    memberName: '',
    email: '',
    language: '',
    tenantId: '',
  })

  // Form data
  const formData = reactive({
    memberName: '',
    email: '',
    password: '',
    passwordConfirm: '',
    language: 'ja', // Default to Japanese
  })

  // Form errors
  const errors = reactive({
    memberName: '',
    email: '',
    password: '',
    passwordConfirm: '',
    language: '',
  })

  // Language options for auction users
  const languageOptions = [
    {value: 'ja', label: '日本語'},
    {value: 'en', label: 'English'},
  ]

  // Helper function to get language name
  const getLanguageName = langCode => {
    const lang = languageOptions.find(l => l.value === langCode)
    return lang ? lang.label : langCode
  }

  // Email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  // Password validation regex - requires uppercase, lowercase, number, and special character
  const passwordRegex =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/

  // Validation functions
  const validateMemberName = value => {
    if (!value) return '会員名を入力してください'
    if (value.length < 2) return '会員名は2文字以上で入力してください'
    if (value.length > 100) return '会員名は100文字以下で入力してください'
    return ''
  }

  const validateEmail = value => {
    if (!value) return 'メールアドレスを入力してください'
    if (!emailRegex.test(value)) return '有効なメールアドレスを入力してください'
    return ''
  }

  const validatePassword = value => {
    if (!value) return 'パスワードを入力してください'
    if (value.length < 8) return 'パスワードは8文字以上で入力してください'
    if (value.length > 50) return 'パスワードは50文字以下で入力してください'
    if (!passwordRegex.test(value)) {
      return 'パスワードは大文字、小文字、数字、特殊文字を含む必要があります'
    }
    return ''
  }

  const validatePasswordConfirm = value => {
    if (!value) return 'パスワード確認を入力してください'
    if (value !== formData.password) return 'パスワードが一致しません'
    return ''
  }

  const validateLanguage = value => {
    if (!value) return '言語設定を選択してください'
    return ''
  }

  // Validate individual field
  const validateField = fieldName => {
    const value = formData[fieldName]
    switch (fieldName) {
      case 'memberName':
        errors.memberName = validateMemberName(value)
        break
      case 'email':
        errors.email = validateEmail(value)
        break
      case 'password':
        errors.password = validatePassword(value)
        // Re-validate password confirmation if it exists
        if (formData.passwordConfirm) {
          errors.passwordConfirm = validatePasswordConfirm(
            formData.passwordConfirm
          )
        }
        break
      case 'passwordConfirm':
        errors.passwordConfirm = validatePasswordConfirm(value)
        break
      case 'language':
        errors.language = validateLanguage(value)
        break
    }
  }

  // Check if form is valid
  const isFormValid = computed(() => {
    return (
      formData.memberName &&
      formData.email &&
      formData.password &&
      formData.passwordConfirm &&
      formData.language &&
      !errors.memberName &&
      !errors.email &&
      !errors.password &&
      !errors.passwordConfirm &&
      !errors.language
    )
  })

  // Register function
  const register = async () => {
    registerMsg.value = []
    loading.value = true

    try {
      const apiEndpoint = `${import.meta.env.VITE_API_ENDPOINT}cognito-register-member`
      const response = await apiExecute(apiEndpoint, 'POST', {
        registerData: {
          memberName: formData.memberName,
          email: formData.email,
          password: formData.password,
          passwordConfirm: formData.passwordConfirm,
          language: formData.language,
        },
        languageCode: formData.language,
      })

      console.log('Registration response:', response)

      if (response) {
        createdUser.value = {
          memberName: formData.memberName,
          email: formData.email,
          language: formData.language,
          tenantId: response.tenantId || '1', // Default tenant
        }
        registrationSuccess.value = true
      }
      loading.value = false
    } catch (error) {
      console.error('Registration error:', error)
      loading.value = false

      // Handle specific Cognito errors
      if (error.response?.data) {
        const errorData = error.response.data
        if (errorData.name === 'Email Already Exists') {
          registerMsg.value = ['このメールアドレスは既に登録されています']
        } else if (errorData.name === 'Password Mismatch') {
          registerMsg.value = ['パスワードと確認用パスワードが一致しません']
        } else if (errorData.name === 'Registration Error') {
          registerMsg.value = [errorData.message || '登録に失敗しました']
        } else {
          registerMsg.value = [
            errorData.message || '登録中にエラーが発生しました',
          ]
        }
      } else {
        registerMsg.value = [
          'ネットワークエラーが発生しました。もう一度お試しください。',
        ]
      }
    }
  }

  const onSubmit = () => {
    // Validate all fields before submission
    Object.keys(formData).forEach(fieldName => {
      validateField(fieldName)
    })

    // Only submit if form is valid
    if (isFormValid.value) {
      register()
    }
  }

  const backToRegister = () => {
    registrationSuccess.value = false
    // Reset form data
    Object.keys(formData).forEach(key => {
      formData[key] = key === 'language' ? 'ja' : ''
    })
    // Reset errors
    Object.keys(errors).forEach(key => {
      errors[key] = ''
    })
  }

  const navigateToLogin = () => {
    router.push('/login')
  }
</script>

<style scoped>
  .min-vh-100 {
    min-height: 100vh;
  }

  .bg-light {
    background-color: #f8f9fa !important;
  }

  .input-group-text {
    background-color: #e9ecef;
    border-color: #ced4da;
  }

  .form-control:focus,
  .form-select:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  }

  .is-invalid {
    border-color: #dc3545;
  }

  .invalid-feedback {
    color: #dc3545;
    font-size: 0.875em;
    margin-top: 0.25rem;
  }
</style>
