variable "project_name" {
  description = "Project name"
}

variable "environment" {
  description = "the environment name such as prod or stage"
}

variable "external_domain_name" {
  description = "external_domain_name"
}

variable "domain_name" {
  description = "domain_name"
}

variable "s3-bucket-arn" {
  description = "File upload bucket arn"
}

variable "s3-bucket-id" {
  description = "File upload bucket id"
}

variable "bucket_regional_domain_name" {
  description = "File upload bucket bucket_regional_domain_name"
}

variable "api_gateway_stage_name" {
  description = "api_gateway_stage_name"
  default     = "api"
}

variable "slack-notification-sns-topic-arn" {
  description = "slack-notification-sns-topic-arn"
}

variable "s3-bucket-bucket" {
  description = "File upload bucket bucket"
}

variable "api_gateway_5xx_alarm_slack_hook_url" {
  description = "api_gateway_5xx_alarm_slack_hook_url"
  default     = ""
}

variable "lambda_subnet_ids" {
  description = "lambda_subnet_ids"
  default     = []
}

variable "lambda_security_group_id" {
  description = "lambda_security_group_id"
  default     = ""
}

variable "lambda_global_environment_variables" {
  description = "lambda_global_environment_variables"
  default     = {}
}

variable "slack-notification-lambda-arn" {
  description = "slack-notification-lambda-arn"
}

variable "common_lambda_layer" {
  description = "common_lambda_layer"
}

variable "profile_name" {
  description = "profile_name"
}

variable "record_name" {
  description = "record_name"
  default = ""
}

variable "mail_from_domain" {
  description = "mail_from_domain"
}

variable "libreoffice_lib_layer" {
  description = "libreoffice_lib_layer"
}

variable "convert_xlsx_to_pdf_endpoint_lambda_arn" {
  description = "convert_xlsx_to_pdf_endpoint_lambda_arn"
}

variable "basic_auth_enable" {
  description = "basic_auth_enable"
}

variable "cognito_user_pool_arn" {
  description = "cognito_user_pool_arn"
}

variable "cognito_user_pool_id" {
  description = "cognito_user_pool_id"
}

variable "cognito_client_id" {
  description = "cognito_client_id"
}

variable "auction_tenants" {
  description = "auction_tenants"
  type = map(object({
    tenant_id        = string
    use_custom_domain = bool
    domain_name       = string
    hosted_zone_name  = string
  }))
}

variable "auction_custom_domains" {
  description = "auction_custom_domains"
  type = list(string)
}

variable "auction_acm_domain_name" {
  description = "auction_acm_domain_name"
  type = string
}
