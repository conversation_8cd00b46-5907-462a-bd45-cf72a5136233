<template>
  <h2 class="page-ttl">
    <p class="ttl">会員登録</p>
    <p class="sub">Cognito Registration</p>
  </h2>

  <div class="container">
    <!-- Success Message Component -->
    <section v-if="registrationSuccess" id="registration-success">
      <div class="success-message">
        <div class="success-icon">
          <span class="check-mark">✓</span>
        </div>
        <h3>会員登録が完了しました</h3>
        <p>以下の情報でアカウントが作成されました</p>

        <div class="user-details">
          <table class="tbl-success">
            <tbody>
              <tr>
                <th>会員名</th>
                <td>{{ createdUser.memberName }}</td>
              </tr>
              <tr>
                <th>メールアドレス</th>
                <td>{{ createdUser.email }}</td>
              </tr>
              <tr>
                <th>言語設定</th>
                <td>{{ getLanguageName(createdUser.language) }}</td>
              </tr>
              <tr>
                <th>テナントID</th>
                <td>{{ createdUser.tenantId }}</td>
              </tr>
            </tbody>
          </table>
        </div>

        <div class="success-actions">
          <button class="btn-secondary" @click="backToRegister">戻る</button>
          <button class="btn-primary" @click="navigateToLogin">
            ログイン画面へ
          </button>
        </div>
      </div>
    </section>

    <!-- Registration Form -->
    <section v-else id="cognito-register-form">
      <form @submit.prevent="onSubmit">
        <div class="form-description">
          <p>オークション会員アカウントを作成してください</p>
        </div>

        <!-- Error Messages -->
        <div v-if="registerMsg.length > 0" class="error-messages">
          <div v-for="(msg, index) in registerMsg" :key="index" class="err-txt">
            {{ msg }}
          </div>
        </div>

        <table class="tbl-register">
          <tbody>
            <!-- 会員名 -->
            <tr>
              <th>会員名<em class="req">※必須</em></th>
              <td>
                <input
                  v-model="formData.memberName"
                  type="text"
                  :class="['ime-dis', {err: errors.memberName}]"
                  placeholder="会員名を入力してください"
                  @blur="validateField('memberName')"
                />
                <p v-if="errors.memberName" class="err-txt">
                  {{ errors.memberName }}
                </p>
              </td>
            </tr>

            <!-- メールアドレス -->
            <tr>
              <th>メールアドレス<em class="req">※必須</em></th>
              <td>
                <input
                  v-model="formData.email"
                  type="email"
                  :class="['ime-dis', {err: errors.email}]"
                  placeholder="<EMAIL>"
                  @blur="validateField('email')"
                />
                <p v-if="errors.email" class="err-txt">{{ errors.email }}</p>
              </td>
            </tr>

            <!-- パスワード -->
            <tr>
              <th>パスワード<em class="req">※必須</em></th>
              <td>
                <input
                  v-model="formData.password"
                  type="password"
                  :class="['ime-dis', {err: errors.password}]"
                  placeholder="8文字以上の英数字・記号"
                  @blur="validateField('password')"
                />
                <p v-if="errors.password" class="err-txt">
                  {{ errors.password }}
                </p>
              </td>
            </tr>

            <!-- パスワード（確認用） -->
            <tr>
              <th>パスワード（確認用）<em class="req">※必須</em></th>
              <td>
                <input
                  v-model="formData.passwordConfirm"
                  type="password"
                  :class="['ime-dis', {err: errors.passwordConfirm}]"
                  placeholder="上記と同じパスワード"
                  @blur="validateField('passwordConfirm')"
                />
                <p v-if="errors.passwordConfirm" class="err-txt">
                  {{ errors.passwordConfirm }}
                </p>
              </td>
            </tr>

            <!-- 言語設定 -->
            <tr>
              <th>言語設定<em class="req">※必須</em></th>
              <td>
                <select
                  v-model="formData.language"
                  :class="['ime-dis', {err: errors.language}]"
                  @blur="validateField('language')"
                >
                  <option value="">言語を選択してください</option>
                  <option
                    v-for="opt in languageOptions"
                    :key="opt.value"
                    :value="opt.value"
                  >
                    {{ opt.label }}
                  </option>
                </select>
                <p v-if="errors.language" class="err-txt">
                  {{ errors.language }}
                </p>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- 登録ボタン -->
        <div class="register-button">
          <input
            type="submit"
            :disabled="loading || !isFormValid"
            :value="loading ? '登録中...' : 'アカウント作成'"
            class="btn-register"
          />
        </div>

        <!-- ログインリンク -->
        <div class="login-link">
          <a @click="navigateToLogin" class="register-btt">
            既存アカウントでログイン
          </a>
        </div>
      </form>
    </section>
  </div>
</template>

<script setup>
  defineOptions({name: 'CognitoRegisterPage'})
  import {computed, reactive, ref} from 'vue'
  import {useRouter} from 'vue-router'
  import useApi from '../../composables/useApi'
  import {useCognitoAuthStore} from '../../stores/cognitoAuth'

  const router = useRouter()
  const cognitoAuth = useCognitoAuthStore()
  const {apiExecute} = useApi()

  const registerMsg = ref([])
  const loading = ref(false)
  const registrationSuccess = ref(false)
  const createdUser = ref({
    memberName: '',
    email: '',
    language: '',
    tenantId: '',
  })

  // Form data
  const formData = reactive({
    memberName: '',
    email: '',
    password: '',
    passwordConfirm: '',
    language: 'ja', // Default to Japanese
  })

  // Form errors
  const errors = reactive({
    memberName: '',
    email: '',
    password: '',
    passwordConfirm: '',
    language: '',
  })

  // Language options for auction users
  const languageOptions = [
    {value: 'ja', label: '日本語'},
    {value: 'en', label: 'English'},
  ]

  // Helper function to get language name
  const getLanguageName = langCode => {
    const lang = languageOptions.find(l => l.value === langCode)
    return lang ? lang.label : langCode
  }

  // Email validation regex
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

  // Password validation regex - requires uppercase, lowercase, number, and special character
  const passwordRegex =
    /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/

  // Validation functions
  const validateMemberName = value => {
    if (!value) return '会員名を入力してください'
    if (value.length < 2) return '会員名は2文字以上で入力してください'
    if (value.length > 100) return '会員名は100文字以下で入力してください'
    return ''
  }

  const validateEmail = value => {
    if (!value) return 'メールアドレスを入力してください'
    if (!emailRegex.test(value)) return '有効なメールアドレスを入力してください'
    return ''
  }

  const validatePassword = value => {
    if (!value) return 'パスワードを入力してください'
    if (value.length < 8) return 'パスワードは8文字以上で入力してください'
    if (value.length > 50) return 'パスワードは50文字以下で入力してください'
    if (!passwordRegex.test(value)) {
      return 'パスワードは大文字、小文字、数字、特殊文字を含む必要があります'
    }
    return ''
  }

  const validatePasswordConfirm = value => {
    if (!value) return 'パスワード確認を入力してください'
    if (value !== formData.password) return 'パスワードが一致しません'
    return ''
  }

  const validateLanguage = value => {
    if (!value) return '言語設定を選択してください'
    return ''
  }

  // Validate individual field
  const validateField = fieldName => {
    const value = formData[fieldName]
    switch (fieldName) {
      case 'memberName':
        errors.memberName = validateMemberName(value)
        break
      case 'email':
        errors.email = validateEmail(value)
        break
      case 'password':
        errors.password = validatePassword(value)
        // Re-validate password confirmation if it exists
        if (formData.passwordConfirm) {
          errors.passwordConfirm = validatePasswordConfirm(
            formData.passwordConfirm
          )
        }
        break
      case 'passwordConfirm':
        errors.passwordConfirm = validatePasswordConfirm(value)
        break
      case 'language':
        errors.language = validateLanguage(value)
        break
    }
  }

  // Check if form is valid
  const isFormValid = computed(() => {
    return (
      formData.memberName &&
      formData.email &&
      formData.password &&
      formData.passwordConfirm &&
      formData.language &&
      !errors.memberName &&
      !errors.email &&
      !errors.password &&
      !errors.passwordConfirm &&
      !errors.language
    )
  })

  // Register function
  const register = async () => {
    registerMsg.value = []
    loading.value = true

    try {
      const apiEndpoint = `${import.meta.env.VITE_API_ENDPOINT}cognito-register-member`
      const response = await apiExecute(apiEndpoint, 'POST', {
        registerData: {
          memberName: formData.memberName,
          email: formData.email,
          password: formData.password,
          passwordConfirm: formData.passwordConfirm,
          language: formData.language,
        },
        languageCode: formData.language,
      })

      console.log('Registration response:', response)

      if (response) {
        createdUser.value = {
          memberName: formData.memberName,
          email: formData.email,
          language: formData.language,
          tenantId: response.tenantId || '1', // Default tenant
        }
        registrationSuccess.value = true
      }
      loading.value = false
    } catch (error) {
      console.error('Registration error:', error)
      loading.value = false

      // Handle specific Cognito errors
      if (error.response?.data) {
        const errorData = error.response.data
        if (errorData.name === 'Email Already Exists') {
          registerMsg.value = ['このメールアドレスは既に登録されています']
        } else if (errorData.name === 'Password Mismatch') {
          registerMsg.value = ['パスワードと確認用パスワードが一致しません']
        } else if (errorData.name === 'Registration Error') {
          registerMsg.value = [errorData.message || '登録に失敗しました']
        } else {
          registerMsg.value = [
            errorData.message || '登録中にエラーが発生しました',
          ]
        }
      } else {
        registerMsg.value = [
          'ネットワークエラーが発生しました。もう一度お試しください。',
        ]
      }
    }
  }

  const onSubmit = () => {
    // Validate all fields before submission
    Object.keys(formData).forEach(fieldName => {
      validateField(fieldName)
    })

    // Only submit if form is valid
    if (isFormValid.value) {
      register()
    }
  }

  const backToRegister = () => {
    registrationSuccess.value = false
    // Reset form data
    Object.keys(formData).forEach(key => {
      formData[key] = key === 'language' ? 'ja' : ''
    })
    // Reset errors
    Object.keys(errors).forEach(key => {
      errors[key] = ''
    })
  }

  const navigateToLogin = () => {
    router.push('/login')
  }
</script>

<style scoped>
  /* Success Message Styles */
  #registration-success {
    margin: 40px auto;
    max-width: 600px;
    text-align: center;
  }

  .success-message {
    border: 1px solid #28a745;
    background-color: #f8fff9;
    padding: 40px 30px;
    border-radius: 8px;
  }

  .success-icon {
    margin-bottom: 20px;
  }

  .check-mark {
    display: inline-block;
    width: 60px;
    height: 60px;
    background-color: #28a745;
    color: white;
    border-radius: 50%;
    line-height: 60px;
    font-size: 30px;
    font-weight: bold;
  }

  .success-message h3 {
    color: #427fae;
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 15px;
  }

  .success-message p {
    color: #666;
    margin-bottom: 25px;
  }

  .tbl-success {
    width: 100%;
    margin: 20px 0;
    border-collapse: collapse;
  }

  .tbl-success th,
  .tbl-success td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
  }

  .tbl-success th {
    background-color: #f5f5f5;
    font-weight: 600;
    width: 30%;
  }

  .success-actions {
    margin-top: 30px;
    display: flex;
    justify-content: space-between;
    gap: 15px;
  }

  .btn-secondary,
  .btn-primary {
    padding: 12px 24px;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .btn-secondary {
    background-color: #6c757d;
    color: white;
  }

  .btn-secondary:hover {
    background-color: #5a6268;
  }

  .btn-primary {
    background-color: #427fae;
    color: white;
  }

  .btn-primary:hover {
    background-color: #356a94;
  }

  /* Form Styles */
  #cognito-register-form {
    margin: 40px auto;
    max-width: 600px;
  }

  .form-description {
    text-align: center;
    margin-bottom: 30px;
  }

  .form-description p {
    font-size: 18px;
    color: #427fae;
    font-weight: 600;
  }

  .error-messages {
    margin-bottom: 20px;
    text-align: center;
  }

  .err-txt {
    color: #ff0000;
    font-weight: 500;
    margin: 5px 0;
  }

  .tbl-register {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 30px;
  }

  .tbl-register tr {
    display: flex;
    flex-direction: column;
    margin-bottom: 20px;
  }

  .tbl-register th {
    display: block;
    position: relative;
    width: 100%;
    padding: 8px 12px;
    font-size: 16px;
    font-weight: 600;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-bottom: none;
  }

  .tbl-register td {
    display: block;
    width: 100%;
    padding: 0;
    border: 1px solid #ddd;
    border-top: none;
  }

  .tbl-register input,
  .tbl-register select {
    width: 100%;
    padding: 12px;
    border: none;
    font-size: 16px;
    outline: none;
    background-color: white;
  }

  .tbl-register input.err,
  .tbl-register select.err {
    background-color: #ffe6e6;
    border-left: 4px solid #ff0000;
  }

  .tbl-register input:focus,
  .tbl-register select:focus {
    background-color: #f0f8ff;
    border-left: 4px solid #427fae;
  }

  .req {
    position: absolute;
    top: 8px;
    right: 12px;
    font-size: 12px;
    font-weight: 700;
    color: #f00;
  }

  .register-button {
    text-align: center;
    margin-bottom: 20px;
  }

  .btn-register {
    background-color: #427fae;
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 18px;
    font-weight: 600;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
  }

  .btn-register:hover:not(:disabled) {
    background-color: #356a94;
  }

  .btn-register:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }

  .login-link {
    text-align: center;
  }

  .register-btt {
    color: #427fae;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
  }

  .register-btt:hover {
    text-decoration: underline;
  }

  /* Mobile Responsive */
  @media screen and (max-width: 767px) {
    #registration-success,
    #cognito-register-form {
      margin: 20px 15px;
      max-width: none;
    }

    .success-message {
      padding: 20px 15px;
    }

    .success-actions {
      flex-direction: column;
    }

    .btn-secondary,
    .btn-primary {
      width: 100%;
      margin-bottom: 10px;
    }

    .tbl-register th {
      font-size: 14px;
      padding: 6px 10px;
    }

    .tbl-register input,
    .tbl-register select {
      padding: 10px;
      font-size: 14px;
    }

    .req {
      font-size: 10px;
      top: 6px;
      right: 10px;
    }

    .btn-register {
      padding: 12px 30px;
      font-size: 16px;
    }
  }
</style>
